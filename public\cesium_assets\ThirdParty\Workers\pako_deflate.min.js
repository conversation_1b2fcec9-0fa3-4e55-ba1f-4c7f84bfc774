/*! pako 2.1.0 https://github.com/nodeca/pako @license (MIT AND Zlib) */
!(function(t, e) {
  "object" == typeof exports && "undefined" != typeof module ? e(exports) : "function" == typeof define && define.amd ? define(["exports"], e) : e((t = "undefined" != typeof globalThis ? globalThis : t || self).pako = {});
})(this, (function(t) {
  "use strict";
  function e(t2) {
    let e2 = t2.length;
    for (; --e2 >= 0; ) t2[e2] = 0;
  }
  const a = 256, s = 286, n = 30, r = 15, i = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0]), _ = new Uint8Array([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13]), l = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7]), h = new Uint8Array([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]), o = new Array(576);
  e(o);
  const d = new Array(60);
  e(d);
  const u = new Array(512);
  e(u);
  const f = new Array(256);
  e(f);
  const c = new Array(29);
  e(c);
  const p = new Array(n);
  function g(t2, e2, a2, s2, n2) {
    this.static_tree = t2, this.extra_bits = e2, this.extra_base = a2, this.elems = s2, this.max_length = n2, this.has_stree = t2 && t2.length;
  }
  let w, m, b;
  function y(t2, e2) {
    this.dyn_tree = t2, this.max_code = 0, this.stat_desc = e2;
  }
  e(p);
  const v = (t2) => t2 < 256 ? u[t2] : u[256 + (t2 >>> 7)], z = (t2, e2) => {
    t2.pending_buf[t2.pending++] = 255 & e2, t2.pending_buf[t2.pending++] = e2 >>> 8 & 255;
  }, k = (t2, e2, a2) => {
    t2.bi_valid > 16 - a2 ? (t2.bi_buf |= e2 << t2.bi_valid & 65535, z(t2, t2.bi_buf), t2.bi_buf = e2 >> 16 - t2.bi_valid, t2.bi_valid += a2 - 16) : (t2.bi_buf |= e2 << t2.bi_valid & 65535, t2.bi_valid += a2);
  }, x = (t2, e2, a2) => {
    k(t2, a2[2 * e2], a2[2 * e2 + 1]);
  }, A = (t2, e2) => {
    let a2 = 0;
    do {
      a2 |= 1 & t2, t2 >>>= 1, a2 <<= 1;
    } while (--e2 > 0);
    return a2 >>> 1;
  }, E = (t2, e2, a2) => {
    const s2 = new Array(16);
    let n2, i2, _2 = 0;
    for (n2 = 1; n2 <= r; n2++) _2 = _2 + a2[n2 - 1] << 1, s2[n2] = _2;
    for (i2 = 0; i2 <= e2; i2++) {
      let e3 = t2[2 * i2 + 1];
      0 !== e3 && (t2[2 * i2] = A(s2[e3]++, e3));
    }
  }, Z = (t2) => {
    let e2;
    for (e2 = 0; e2 < s; e2++) t2.dyn_ltree[2 * e2] = 0;
    for (e2 = 0; e2 < n; e2++) t2.dyn_dtree[2 * e2] = 0;
    for (e2 = 0; e2 < 19; e2++) t2.bl_tree[2 * e2] = 0;
    t2.dyn_ltree[512] = 1, t2.opt_len = t2.static_len = 0, t2.sym_next = t2.matches = 0;
  }, U = (t2) => {
    t2.bi_valid > 8 ? z(t2, t2.bi_buf) : t2.bi_valid > 0 && (t2.pending_buf[t2.pending++] = t2.bi_buf), t2.bi_buf = 0, t2.bi_valid = 0;
  }, R = (t2, e2, a2, s2) => {
    const n2 = 2 * e2, r2 = 2 * a2;
    return t2[n2] < t2[r2] || t2[n2] === t2[r2] && s2[e2] <= s2[a2];
  }, S = (t2, e2, a2) => {
    const s2 = t2.heap[a2];
    let n2 = a2 << 1;
    for (; n2 <= t2.heap_len && (n2 < t2.heap_len && R(e2, t2.heap[n2 + 1], t2.heap[n2], t2.depth) && n2++, !R(e2, s2, t2.heap[n2], t2.depth)); ) t2.heap[a2] = t2.heap[n2], a2 = n2, n2 <<= 1;
    t2.heap[a2] = s2;
  }, T = (t2, e2, s2) => {
    let n2, r2, l2, h2, o2 = 0;
    if (0 !== t2.sym_next) do {
      n2 = 255 & t2.pending_buf[t2.sym_buf + o2++], n2 += (255 & t2.pending_buf[t2.sym_buf + o2++]) << 8, r2 = t2.pending_buf[t2.sym_buf + o2++], 0 === n2 ? x(t2, r2, e2) : (l2 = f[r2], x(t2, l2 + a + 1, e2), h2 = i[l2], 0 !== h2 && (r2 -= c[l2], k(t2, r2, h2)), n2--, l2 = v(n2), x(t2, l2, s2), h2 = _[l2], 0 !== h2 && (n2 -= p[l2], k(t2, n2, h2)));
    } while (o2 < t2.sym_next);
    x(t2, 256, e2);
  }, L = (t2, e2) => {
    const a2 = e2.dyn_tree, s2 = e2.stat_desc.static_tree, n2 = e2.stat_desc.has_stree, i2 = e2.stat_desc.elems;
    let _2, l2, h2, o2 = -1;
    for (t2.heap_len = 0, t2.heap_max = 573, _2 = 0; _2 < i2; _2++) 0 !== a2[2 * _2] ? (t2.heap[++t2.heap_len] = o2 = _2, t2.depth[_2] = 0) : a2[2 * _2 + 1] = 0;
    for (; t2.heap_len < 2; ) h2 = t2.heap[++t2.heap_len] = o2 < 2 ? ++o2 : 0, a2[2 * h2] = 1, t2.depth[h2] = 0, t2.opt_len--, n2 && (t2.static_len -= s2[2 * h2 + 1]);
    for (e2.max_code = o2, _2 = t2.heap_len >> 1; _2 >= 1; _2--) S(t2, a2, _2);
    h2 = i2;
    do {
      _2 = t2.heap[1], t2.heap[1] = t2.heap[t2.heap_len--], S(t2, a2, 1), l2 = t2.heap[1], t2.heap[--t2.heap_max] = _2, t2.heap[--t2.heap_max] = l2, a2[2 * h2] = a2[2 * _2] + a2[2 * l2], t2.depth[h2] = (t2.depth[_2] >= t2.depth[l2] ? t2.depth[_2] : t2.depth[l2]) + 1, a2[2 * _2 + 1] = a2[2 * l2 + 1] = h2, t2.heap[1] = h2++, S(t2, a2, 1);
    } while (t2.heap_len >= 2);
    t2.heap[--t2.heap_max] = t2.heap[1], ((t3, e3) => {
      const a3 = e3.dyn_tree, s3 = e3.max_code, n3 = e3.stat_desc.static_tree, i3 = e3.stat_desc.has_stree, _3 = e3.stat_desc.extra_bits, l3 = e3.stat_desc.extra_base, h3 = e3.stat_desc.max_length;
      let o3, d2, u2, f2, c2, p2, g2 = 0;
      for (f2 = 0; f2 <= r; f2++) t3.bl_count[f2] = 0;
      for (a3[2 * t3.heap[t3.heap_max] + 1] = 0, o3 = t3.heap_max + 1; o3 < 573; o3++) d2 = t3.heap[o3], f2 = a3[2 * a3[2 * d2 + 1] + 1] + 1, f2 > h3 && (f2 = h3, g2++), a3[2 * d2 + 1] = f2, d2 > s3 || (t3.bl_count[f2]++, c2 = 0, d2 >= l3 && (c2 = _3[d2 - l3]), p2 = a3[2 * d2], t3.opt_len += p2 * (f2 + c2), i3 && (t3.static_len += p2 * (n3[2 * d2 + 1] + c2)));
      if (0 !== g2) {
        do {
          for (f2 = h3 - 1; 0 === t3.bl_count[f2]; ) f2--;
          t3.bl_count[f2]--, t3.bl_count[f2 + 1] += 2, t3.bl_count[h3]--, g2 -= 2;
        } while (g2 > 0);
        for (f2 = h3; 0 !== f2; f2--) for (d2 = t3.bl_count[f2]; 0 !== d2; ) u2 = t3.heap[--o3], u2 > s3 || (a3[2 * u2 + 1] !== f2 && (t3.opt_len += (f2 - a3[2 * u2 + 1]) * a3[2 * u2], a3[2 * u2 + 1] = f2), d2--);
      }
    })(t2, e2), E(a2, o2, t2.bl_count);
  }, F = (t2, e2, a2) => {
    let s2, n2, r2 = -1, i2 = e2[1], _2 = 0, l2 = 7, h2 = 4;
    for (0 === i2 && (l2 = 138, h2 = 3), e2[2 * (a2 + 1) + 1] = 65535, s2 = 0; s2 <= a2; s2++) n2 = i2, i2 = e2[2 * (s2 + 1) + 1], ++_2 < l2 && n2 === i2 || (_2 < h2 ? t2.bl_tree[2 * n2] += _2 : 0 !== n2 ? (n2 !== r2 && t2.bl_tree[2 * n2]++, t2.bl_tree[32]++) : _2 <= 10 ? t2.bl_tree[34]++ : t2.bl_tree[36]++, _2 = 0, r2 = n2, 0 === i2 ? (l2 = 138, h2 = 3) : n2 === i2 ? (l2 = 6, h2 = 3) : (l2 = 7, h2 = 4));
  }, O = (t2, e2, a2) => {
    let s2, n2, r2 = -1, i2 = e2[1], _2 = 0, l2 = 7, h2 = 4;
    for (0 === i2 && (l2 = 138, h2 = 3), s2 = 0; s2 <= a2; s2++) if (n2 = i2, i2 = e2[2 * (s2 + 1) + 1], !(++_2 < l2 && n2 === i2)) {
      if (_2 < h2) do {
        x(t2, n2, t2.bl_tree);
      } while (0 != --_2);
      else 0 !== n2 ? (n2 !== r2 && (x(t2, n2, t2.bl_tree), _2--), x(t2, 16, t2.bl_tree), k(t2, _2 - 3, 2)) : _2 <= 10 ? (x(t2, 17, t2.bl_tree), k(t2, _2 - 3, 3)) : (x(t2, 18, t2.bl_tree), k(t2, _2 - 11, 7));
      _2 = 0, r2 = n2, 0 === i2 ? (l2 = 138, h2 = 3) : n2 === i2 ? (l2 = 6, h2 = 3) : (l2 = 7, h2 = 4);
    }
  };
  let D = false;
  const N = (t2, e2, a2, s2) => {
    k(t2, 0 + (s2 ? 1 : 0), 3), U(t2), z(t2, a2), z(t2, ~a2), a2 && t2.pending_buf.set(t2.window.subarray(e2, e2 + a2), t2.pending), t2.pending += a2;
  };
  var I = (t2, e2, s2, n2) => {
    let r2, i2, _2 = 0;
    t2.level > 0 ? (2 === t2.strm.data_type && (t2.strm.data_type = ((t3) => {
      let e3, s3 = 4093624447;
      for (e3 = 0; e3 <= 31; e3++, s3 >>>= 1) if (1 & s3 && 0 !== t3.dyn_ltree[2 * e3]) return 0;
      if (0 !== t3.dyn_ltree[18] || 0 !== t3.dyn_ltree[20] || 0 !== t3.dyn_ltree[26]) return 1;
      for (e3 = 32; e3 < a; e3++) if (0 !== t3.dyn_ltree[2 * e3]) return 1;
      return 0;
    })(t2)), L(t2, t2.l_desc), L(t2, t2.d_desc), _2 = ((t3) => {
      let e3;
      for (F(t3, t3.dyn_ltree, t3.l_desc.max_code), F(t3, t3.dyn_dtree, t3.d_desc.max_code), L(t3, t3.bl_desc), e3 = 18; e3 >= 3 && 0 === t3.bl_tree[2 * h[e3] + 1]; e3--) ;
      return t3.opt_len += 3 * (e3 + 1) + 5 + 5 + 4, e3;
    })(t2), r2 = t2.opt_len + 3 + 7 >>> 3, i2 = t2.static_len + 3 + 7 >>> 3, i2 <= r2 && (r2 = i2)) : r2 = i2 = s2 + 5, s2 + 4 <= r2 && -1 !== e2 ? N(t2, e2, s2, n2) : 4 === t2.strategy || i2 === r2 ? (k(t2, 2 + (n2 ? 1 : 0), 3), T(t2, o, d)) : (k(t2, 4 + (n2 ? 1 : 0), 3), ((t3, e3, a2, s3) => {
      let n3;
      for (k(t3, e3 - 257, 5), k(t3, a2 - 1, 5), k(t3, s3 - 4, 4), n3 = 0; n3 < s3; n3++) k(t3, t3.bl_tree[2 * h[n3] + 1], 3);
      O(t3, t3.dyn_ltree, e3 - 1), O(t3, t3.dyn_dtree, a2 - 1);
    })(t2, t2.l_desc.max_code + 1, t2.d_desc.max_code + 1, _2 + 1), T(t2, t2.dyn_ltree, t2.dyn_dtree)), Z(t2), n2 && U(t2);
  }, C = { _tr_init: (t2) => {
    D || ((() => {
      let t3, e2, a2, h2, y2;
      const v2 = new Array(16);
      for (a2 = 0, h2 = 0; h2 < 28; h2++) for (c[h2] = a2, t3 = 0; t3 < 1 << i[h2]; t3++) f[a2++] = h2;
      for (f[a2 - 1] = h2, y2 = 0, h2 = 0; h2 < 16; h2++) for (p[h2] = y2, t3 = 0; t3 < 1 << _[h2]; t3++) u[y2++] = h2;
      for (y2 >>= 7; h2 < n; h2++) for (p[h2] = y2 << 7, t3 = 0; t3 < 1 << _[h2] - 7; t3++) u[256 + y2++] = h2;
      for (e2 = 0; e2 <= r; e2++) v2[e2] = 0;
      for (t3 = 0; t3 <= 143; ) o[2 * t3 + 1] = 8, t3++, v2[8]++;
      for (; t3 <= 255; ) o[2 * t3 + 1] = 9, t3++, v2[9]++;
      for (; t3 <= 279; ) o[2 * t3 + 1] = 7, t3++, v2[7]++;
      for (; t3 <= 287; ) o[2 * t3 + 1] = 8, t3++, v2[8]++;
      for (E(o, 287, v2), t3 = 0; t3 < n; t3++) d[2 * t3 + 1] = 5, d[2 * t3] = A(t3, 5);
      w = new g(o, i, 257, s, r), m = new g(d, _, 0, n, r), b = new g(new Array(0), l, 0, 19, 7);
    })(), D = true), t2.l_desc = new y(t2.dyn_ltree, w), t2.d_desc = new y(t2.dyn_dtree, m), t2.bl_desc = new y(t2.bl_tree, b), t2.bi_buf = 0, t2.bi_valid = 0, Z(t2);
  }, _tr_stored_block: N, _tr_flush_block: I, _tr_tally: (t2, e2, s2) => (t2.pending_buf[t2.sym_buf + t2.sym_next++] = e2, t2.pending_buf[t2.sym_buf + t2.sym_next++] = e2 >> 8, t2.pending_buf[t2.sym_buf + t2.sym_next++] = s2, 0 === e2 ? t2.dyn_ltree[2 * s2]++ : (t2.matches++, e2--, t2.dyn_ltree[2 * (f[s2] + a + 1)]++, t2.dyn_dtree[2 * v(e2)]++), t2.sym_next === t2.sym_end), _tr_align: (t2) => {
    k(t2, 2, 3), x(t2, 256, o), ((t3) => {
      16 === t3.bi_valid ? (z(t3, t3.bi_buf), t3.bi_buf = 0, t3.bi_valid = 0) : t3.bi_valid >= 8 && (t3.pending_buf[t3.pending++] = 255 & t3.bi_buf, t3.bi_buf >>= 8, t3.bi_valid -= 8);
    })(t2);
  } };
  var B = (t2, e2, a2, s2) => {
    let n2 = 65535 & t2 | 0, r2 = t2 >>> 16 & 65535 | 0, i2 = 0;
    for (; 0 !== a2; ) {
      i2 = a2 > 2e3 ? 2e3 : a2, a2 -= i2;
      do {
        n2 = n2 + e2[s2++] | 0, r2 = r2 + n2 | 0;
      } while (--i2);
      n2 %= 65521, r2 %= 65521;
    }
    return n2 | r2 << 16 | 0;
  };
  const H = new Uint32Array((() => {
    let t2, e2 = [];
    for (var a2 = 0; a2 < 256; a2++) {
      t2 = a2;
      for (var s2 = 0; s2 < 8; s2++) t2 = 1 & t2 ? 3988292384 ^ t2 >>> 1 : t2 >>> 1;
      e2[a2] = t2;
    }
    return e2;
  })());
  var M = (t2, e2, a2, s2) => {
    const n2 = H, r2 = s2 + a2;
    t2 ^= -1;
    for (let a3 = s2; a3 < r2; a3++) t2 = t2 >>> 8 ^ n2[255 & (t2 ^ e2[a3])];
    return -1 ^ t2;
  }, P = { 2: "need dictionary", 1: "stream end", 0: "", "-1": "file error", "-2": "stream error", "-3": "data error", "-4": "insufficient memory", "-5": "buffer error", "-6": "incompatible version" }, j = { Z_NO_FLUSH: 0, Z_PARTIAL_FLUSH: 1, Z_SYNC_FLUSH: 2, Z_FULL_FLUSH: 3, Z_FINISH: 4, Z_BLOCK: 5, Z_TREES: 6, Z_OK: 0, Z_STREAM_END: 1, Z_NEED_DICT: 2, Z_ERRNO: -1, Z_STREAM_ERROR: -2, Z_DATA_ERROR: -3, Z_MEM_ERROR: -4, Z_BUF_ERROR: -5, Z_NO_COMPRESSION: 0, Z_BEST_SPEED: 1, Z_BEST_COMPRESSION: 9, Z_DEFAULT_COMPRESSION: -1, Z_FILTERED: 1, Z_HUFFMAN_ONLY: 2, Z_RLE: 3, Z_FIXED: 4, Z_DEFAULT_STRATEGY: 0, Z_BINARY: 0, Z_TEXT: 1, Z_UNKNOWN: 2, Z_DEFLATED: 8 };
  const { _tr_init: K, _tr_stored_block: Y, _tr_flush_block: G, _tr_tally: X, _tr_align: W } = C, { Z_NO_FLUSH: q, Z_PARTIAL_FLUSH: J, Z_FULL_FLUSH: Q, Z_FINISH: V, Z_BLOCK: $, Z_OK: tt, Z_STREAM_END: et, Z_STREAM_ERROR: at, Z_DATA_ERROR: st, Z_BUF_ERROR: nt, Z_DEFAULT_COMPRESSION: rt, Z_FILTERED: it, Z_HUFFMAN_ONLY: _t, Z_RLE: lt, Z_FIXED: ht, Z_DEFAULT_STRATEGY: ot, Z_UNKNOWN: dt, Z_DEFLATED: ut } = j, ft = 258, ct = 262, pt = 42, gt = 113, wt = 666, mt = (t2, e2) => (t2.msg = P[e2], e2), bt = (t2) => 2 * t2 - (t2 > 4 ? 9 : 0), yt = (t2) => {
    let e2 = t2.length;
    for (; --e2 >= 0; ) t2[e2] = 0;
  }, vt = (t2) => {
    let e2, a2, s2, n2 = t2.w_size;
    e2 = t2.hash_size, s2 = e2;
    do {
      a2 = t2.head[--s2], t2.head[s2] = a2 >= n2 ? a2 - n2 : 0;
    } while (--e2);
    e2 = n2, s2 = e2;
    do {
      a2 = t2.prev[--s2], t2.prev[s2] = a2 >= n2 ? a2 - n2 : 0;
    } while (--e2);
  };
  let zt = (t2, e2, a2) => (e2 << t2.hash_shift ^ a2) & t2.hash_mask;
  const kt = (t2) => {
    const e2 = t2.state;
    let a2 = e2.pending;
    a2 > t2.avail_out && (a2 = t2.avail_out), 0 !== a2 && (t2.output.set(e2.pending_buf.subarray(e2.pending_out, e2.pending_out + a2), t2.next_out), t2.next_out += a2, e2.pending_out += a2, t2.total_out += a2, t2.avail_out -= a2, e2.pending -= a2, 0 === e2.pending && (e2.pending_out = 0));
  }, xt = (t2, e2) => {
    G(t2, t2.block_start >= 0 ? t2.block_start : -1, t2.strstart - t2.block_start, e2), t2.block_start = t2.strstart, kt(t2.strm);
  }, At = (t2, e2) => {
    t2.pending_buf[t2.pending++] = e2;
  }, Et = (t2, e2) => {
    t2.pending_buf[t2.pending++] = e2 >>> 8 & 255, t2.pending_buf[t2.pending++] = 255 & e2;
  }, Zt = (t2, e2, a2, s2) => {
    let n2 = t2.avail_in;
    return n2 > s2 && (n2 = s2), 0 === n2 ? 0 : (t2.avail_in -= n2, e2.set(t2.input.subarray(t2.next_in, t2.next_in + n2), a2), 1 === t2.state.wrap ? t2.adler = B(t2.adler, e2, n2, a2) : 2 === t2.state.wrap && (t2.adler = M(t2.adler, e2, n2, a2)), t2.next_in += n2, t2.total_in += n2, n2);
  }, Ut = (t2, e2) => {
    let a2, s2, n2 = t2.max_chain_length, r2 = t2.strstart, i2 = t2.prev_length, _2 = t2.nice_match;
    const l2 = t2.strstart > t2.w_size - ct ? t2.strstart - (t2.w_size - ct) : 0, h2 = t2.window, o2 = t2.w_mask, d2 = t2.prev, u2 = t2.strstart + ft;
    let f2 = h2[r2 + i2 - 1], c2 = h2[r2 + i2];
    t2.prev_length >= t2.good_match && (n2 >>= 2), _2 > t2.lookahead && (_2 = t2.lookahead);
    do {
      if (a2 = e2, h2[a2 + i2] === c2 && h2[a2 + i2 - 1] === f2 && h2[a2] === h2[r2] && h2[++a2] === h2[r2 + 1]) {
        r2 += 2, a2++;
        do {
        } while (h2[++r2] === h2[++a2] && h2[++r2] === h2[++a2] && h2[++r2] === h2[++a2] && h2[++r2] === h2[++a2] && h2[++r2] === h2[++a2] && h2[++r2] === h2[++a2] && h2[++r2] === h2[++a2] && h2[++r2] === h2[++a2] && r2 < u2);
        if (s2 = ft - (u2 - r2), r2 = u2 - ft, s2 > i2) {
          if (t2.match_start = e2, i2 = s2, s2 >= _2) break;
          f2 = h2[r2 + i2 - 1], c2 = h2[r2 + i2];
        }
      }
    } while ((e2 = d2[e2 & o2]) > l2 && 0 != --n2);
    return i2 <= t2.lookahead ? i2 : t2.lookahead;
  }, Rt = (t2) => {
    const e2 = t2.w_size;
    let a2, s2, n2;
    do {
      if (s2 = t2.window_size - t2.lookahead - t2.strstart, t2.strstart >= e2 + (e2 - ct) && (t2.window.set(t2.window.subarray(e2, e2 + e2 - s2), 0), t2.match_start -= e2, t2.strstart -= e2, t2.block_start -= e2, t2.insert > t2.strstart && (t2.insert = t2.strstart), vt(t2), s2 += e2), 0 === t2.strm.avail_in) break;
      if (a2 = Zt(t2.strm, t2.window, t2.strstart + t2.lookahead, s2), t2.lookahead += a2, t2.lookahead + t2.insert >= 3) for (n2 = t2.strstart - t2.insert, t2.ins_h = t2.window[n2], t2.ins_h = zt(t2, t2.ins_h, t2.window[n2 + 1]); t2.insert && (t2.ins_h = zt(t2, t2.ins_h, t2.window[n2 + 3 - 1]), t2.prev[n2 & t2.w_mask] = t2.head[t2.ins_h], t2.head[t2.ins_h] = n2, n2++, t2.insert--, !(t2.lookahead + t2.insert < 3)); ) ;
    } while (t2.lookahead < ct && 0 !== t2.strm.avail_in);
  }, St = (t2, e2) => {
    let a2, s2, n2, r2 = t2.pending_buf_size - 5 > t2.w_size ? t2.w_size : t2.pending_buf_size - 5, i2 = 0, _2 = t2.strm.avail_in;
    do {
      if (a2 = 65535, n2 = t2.bi_valid + 42 >> 3, t2.strm.avail_out < n2) break;
      if (n2 = t2.strm.avail_out - n2, s2 = t2.strstart - t2.block_start, a2 > s2 + t2.strm.avail_in && (a2 = s2 + t2.strm.avail_in), a2 > n2 && (a2 = n2), a2 < r2 && (0 === a2 && e2 !== V || e2 === q || a2 !== s2 + t2.strm.avail_in)) break;
      i2 = e2 === V && a2 === s2 + t2.strm.avail_in ? 1 : 0, Y(t2, 0, 0, i2), t2.pending_buf[t2.pending - 4] = a2, t2.pending_buf[t2.pending - 3] = a2 >> 8, t2.pending_buf[t2.pending - 2] = ~a2, t2.pending_buf[t2.pending - 1] = ~a2 >> 8, kt(t2.strm), s2 && (s2 > a2 && (s2 = a2), t2.strm.output.set(t2.window.subarray(t2.block_start, t2.block_start + s2), t2.strm.next_out), t2.strm.next_out += s2, t2.strm.avail_out -= s2, t2.strm.total_out += s2, t2.block_start += s2, a2 -= s2), a2 && (Zt(t2.strm, t2.strm.output, t2.strm.next_out, a2), t2.strm.next_out += a2, t2.strm.avail_out -= a2, t2.strm.total_out += a2);
    } while (0 === i2);
    return _2 -= t2.strm.avail_in, _2 && (_2 >= t2.w_size ? (t2.matches = 2, t2.window.set(t2.strm.input.subarray(t2.strm.next_in - t2.w_size, t2.strm.next_in), 0), t2.strstart = t2.w_size, t2.insert = t2.strstart) : (t2.window_size - t2.strstart <= _2 && (t2.strstart -= t2.w_size, t2.window.set(t2.window.subarray(t2.w_size, t2.w_size + t2.strstart), 0), t2.matches < 2 && t2.matches++, t2.insert > t2.strstart && (t2.insert = t2.strstart)), t2.window.set(t2.strm.input.subarray(t2.strm.next_in - _2, t2.strm.next_in), t2.strstart), t2.strstart += _2, t2.insert += _2 > t2.w_size - t2.insert ? t2.w_size - t2.insert : _2), t2.block_start = t2.strstart), t2.high_water < t2.strstart && (t2.high_water = t2.strstart), i2 ? 4 : e2 !== q && e2 !== V && 0 === t2.strm.avail_in && t2.strstart === t2.block_start ? 2 : (n2 = t2.window_size - t2.strstart, t2.strm.avail_in > n2 && t2.block_start >= t2.w_size && (t2.block_start -= t2.w_size, t2.strstart -= t2.w_size, t2.window.set(t2.window.subarray(t2.w_size, t2.w_size + t2.strstart), 0), t2.matches < 2 && t2.matches++, n2 += t2.w_size, t2.insert > t2.strstart && (t2.insert = t2.strstart)), n2 > t2.strm.avail_in && (n2 = t2.strm.avail_in), n2 && (Zt(t2.strm, t2.window, t2.strstart, n2), t2.strstart += n2, t2.insert += n2 > t2.w_size - t2.insert ? t2.w_size - t2.insert : n2), t2.high_water < t2.strstart && (t2.high_water = t2.strstart), n2 = t2.bi_valid + 42 >> 3, n2 = t2.pending_buf_size - n2 > 65535 ? 65535 : t2.pending_buf_size - n2, r2 = n2 > t2.w_size ? t2.w_size : n2, s2 = t2.strstart - t2.block_start, (s2 >= r2 || (s2 || e2 === V) && e2 !== q && 0 === t2.strm.avail_in && s2 <= n2) && (a2 = s2 > n2 ? n2 : s2, i2 = e2 === V && 0 === t2.strm.avail_in && a2 === s2 ? 1 : 0, Y(t2, t2.block_start, a2, i2), t2.block_start += a2, kt(t2.strm)), i2 ? 3 : 1);
  }, Tt = (t2, e2) => {
    let a2, s2;
    for (; ; ) {
      if (t2.lookahead < ct) {
        if (Rt(t2), t2.lookahead < ct && e2 === q) return 1;
        if (0 === t2.lookahead) break;
      }
      if (a2 = 0, t2.lookahead >= 3 && (t2.ins_h = zt(t2, t2.ins_h, t2.window[t2.strstart + 3 - 1]), a2 = t2.prev[t2.strstart & t2.w_mask] = t2.head[t2.ins_h], t2.head[t2.ins_h] = t2.strstart), 0 !== a2 && t2.strstart - a2 <= t2.w_size - ct && (t2.match_length = Ut(t2, a2)), t2.match_length >= 3) if (s2 = X(t2, t2.strstart - t2.match_start, t2.match_length - 3), t2.lookahead -= t2.match_length, t2.match_length <= t2.max_lazy_match && t2.lookahead >= 3) {
        t2.match_length--;
        do {
          t2.strstart++, t2.ins_h = zt(t2, t2.ins_h, t2.window[t2.strstart + 3 - 1]), a2 = t2.prev[t2.strstart & t2.w_mask] = t2.head[t2.ins_h], t2.head[t2.ins_h] = t2.strstart;
        } while (0 != --t2.match_length);
        t2.strstart++;
      } else t2.strstart += t2.match_length, t2.match_length = 0, t2.ins_h = t2.window[t2.strstart], t2.ins_h = zt(t2, t2.ins_h, t2.window[t2.strstart + 1]);
      else s2 = X(t2, 0, t2.window[t2.strstart]), t2.lookahead--, t2.strstart++;
      if (s2 && (xt(t2, false), 0 === t2.strm.avail_out)) return 1;
    }
    return t2.insert = t2.strstart < 2 ? t2.strstart : 2, e2 === V ? (xt(t2, true), 0 === t2.strm.avail_out ? 3 : 4) : t2.sym_next && (xt(t2, false), 0 === t2.strm.avail_out) ? 1 : 2;
  }, Lt = (t2, e2) => {
    let a2, s2, n2;
    for (; ; ) {
      if (t2.lookahead < ct) {
        if (Rt(t2), t2.lookahead < ct && e2 === q) return 1;
        if (0 === t2.lookahead) break;
      }
      if (a2 = 0, t2.lookahead >= 3 && (t2.ins_h = zt(t2, t2.ins_h, t2.window[t2.strstart + 3 - 1]), a2 = t2.prev[t2.strstart & t2.w_mask] = t2.head[t2.ins_h], t2.head[t2.ins_h] = t2.strstart), t2.prev_length = t2.match_length, t2.prev_match = t2.match_start, t2.match_length = 2, 0 !== a2 && t2.prev_length < t2.max_lazy_match && t2.strstart - a2 <= t2.w_size - ct && (t2.match_length = Ut(t2, a2), t2.match_length <= 5 && (t2.strategy === it || 3 === t2.match_length && t2.strstart - t2.match_start > 4096) && (t2.match_length = 2)), t2.prev_length >= 3 && t2.match_length <= t2.prev_length) {
        n2 = t2.strstart + t2.lookahead - 3, s2 = X(t2, t2.strstart - 1 - t2.prev_match, t2.prev_length - 3), t2.lookahead -= t2.prev_length - 1, t2.prev_length -= 2;
        do {
          ++t2.strstart <= n2 && (t2.ins_h = zt(t2, t2.ins_h, t2.window[t2.strstart + 3 - 1]), a2 = t2.prev[t2.strstart & t2.w_mask] = t2.head[t2.ins_h], t2.head[t2.ins_h] = t2.strstart);
        } while (0 != --t2.prev_length);
        if (t2.match_available = 0, t2.match_length = 2, t2.strstart++, s2 && (xt(t2, false), 0 === t2.strm.avail_out)) return 1;
      } else if (t2.match_available) {
        if (s2 = X(t2, 0, t2.window[t2.strstart - 1]), s2 && xt(t2, false), t2.strstart++, t2.lookahead--, 0 === t2.strm.avail_out) return 1;
      } else t2.match_available = 1, t2.strstart++, t2.lookahead--;
    }
    return t2.match_available && (s2 = X(t2, 0, t2.window[t2.strstart - 1]), t2.match_available = 0), t2.insert = t2.strstart < 2 ? t2.strstart : 2, e2 === V ? (xt(t2, true), 0 === t2.strm.avail_out ? 3 : 4) : t2.sym_next && (xt(t2, false), 0 === t2.strm.avail_out) ? 1 : 2;
  };
  function Ft(t2, e2, a2, s2, n2) {
    this.good_length = t2, this.max_lazy = e2, this.nice_length = a2, this.max_chain = s2, this.func = n2;
  }
  const Ot = [new Ft(0, 0, 0, 0, St), new Ft(4, 4, 8, 4, Tt), new Ft(4, 5, 16, 8, Tt), new Ft(4, 6, 32, 32, Tt), new Ft(4, 4, 16, 16, Lt), new Ft(8, 16, 32, 32, Lt), new Ft(8, 16, 128, 128, Lt), new Ft(8, 32, 128, 256, Lt), new Ft(32, 128, 258, 1024, Lt), new Ft(32, 258, 258, 4096, Lt)];
  function Dt() {
    this.strm = null, this.status = 0, this.pending_buf = null, this.pending_buf_size = 0, this.pending_out = 0, this.pending = 0, this.wrap = 0, this.gzhead = null, this.gzindex = 0, this.method = ut, this.last_flush = -1, this.w_size = 0, this.w_bits = 0, this.w_mask = 0, this.window = null, this.window_size = 0, this.prev = null, this.head = null, this.ins_h = 0, this.hash_size = 0, this.hash_bits = 0, this.hash_mask = 0, this.hash_shift = 0, this.block_start = 0, this.match_length = 0, this.prev_match = 0, this.match_available = 0, this.strstart = 0, this.match_start = 0, this.lookahead = 0, this.prev_length = 0, this.max_chain_length = 0, this.max_lazy_match = 0, this.level = 0, this.strategy = 0, this.good_match = 0, this.nice_match = 0, this.dyn_ltree = new Uint16Array(1146), this.dyn_dtree = new Uint16Array(122), this.bl_tree = new Uint16Array(78), yt(this.dyn_ltree), yt(this.dyn_dtree), yt(this.bl_tree), this.l_desc = null, this.d_desc = null, this.bl_desc = null, this.bl_count = new Uint16Array(16), this.heap = new Uint16Array(573), yt(this.heap), this.heap_len = 0, this.heap_max = 0, this.depth = new Uint16Array(573), yt(this.depth), this.sym_buf = 0, this.lit_bufsize = 0, this.sym_next = 0, this.sym_end = 0, this.opt_len = 0, this.static_len = 0, this.matches = 0, this.insert = 0, this.bi_buf = 0, this.bi_valid = 0;
  }
  const Nt = (t2) => {
    if (!t2) return 1;
    const e2 = t2.state;
    return !e2 || e2.strm !== t2 || e2.status !== pt && 57 !== e2.status && 69 !== e2.status && 73 !== e2.status && 91 !== e2.status && 103 !== e2.status && e2.status !== gt && e2.status !== wt ? 1 : 0;
  }, It = (t2) => {
    if (Nt(t2)) return mt(t2, at);
    t2.total_in = t2.total_out = 0, t2.data_type = dt;
    const e2 = t2.state;
    return e2.pending = 0, e2.pending_out = 0, e2.wrap < 0 && (e2.wrap = -e2.wrap), e2.status = 2 === e2.wrap ? 57 : e2.wrap ? pt : gt, t2.adler = 2 === e2.wrap ? 0 : 1, e2.last_flush = -2, K(e2), tt;
  }, Ct = (t2) => {
    const e2 = It(t2);
    var a2;
    return e2 === tt && ((a2 = t2.state).window_size = 2 * a2.w_size, yt(a2.head), a2.max_lazy_match = Ot[a2.level].max_lazy, a2.good_match = Ot[a2.level].good_length, a2.nice_match = Ot[a2.level].nice_length, a2.max_chain_length = Ot[a2.level].max_chain, a2.strstart = 0, a2.block_start = 0, a2.lookahead = 0, a2.insert = 0, a2.match_length = a2.prev_length = 2, a2.match_available = 0, a2.ins_h = 0), e2;
  }, Bt = (t2, e2, a2, s2, n2, r2) => {
    if (!t2) return at;
    let i2 = 1;
    if (e2 === rt && (e2 = 6), s2 < 0 ? (i2 = 0, s2 = -s2) : s2 > 15 && (i2 = 2, s2 -= 16), n2 < 1 || n2 > 9 || a2 !== ut || s2 < 8 || s2 > 15 || e2 < 0 || e2 > 9 || r2 < 0 || r2 > ht || 8 === s2 && 1 !== i2) return mt(t2, at);
    8 === s2 && (s2 = 9);
    const _2 = new Dt();
    return t2.state = _2, _2.strm = t2, _2.status = pt, _2.wrap = i2, _2.gzhead = null, _2.w_bits = s2, _2.w_size = 1 << _2.w_bits, _2.w_mask = _2.w_size - 1, _2.hash_bits = n2 + 7, _2.hash_size = 1 << _2.hash_bits, _2.hash_mask = _2.hash_size - 1, _2.hash_shift = ~~((_2.hash_bits + 3 - 1) / 3), _2.window = new Uint8Array(2 * _2.w_size), _2.head = new Uint16Array(_2.hash_size), _2.prev = new Uint16Array(_2.w_size), _2.lit_bufsize = 1 << n2 + 6, _2.pending_buf_size = 4 * _2.lit_bufsize, _2.pending_buf = new Uint8Array(_2.pending_buf_size), _2.sym_buf = _2.lit_bufsize, _2.sym_end = 3 * (_2.lit_bufsize - 1), _2.level = e2, _2.strategy = r2, _2.method = a2, Ct(t2);
  };
  var Ht = { deflateInit: (t2, e2) => Bt(t2, e2, ut, 15, 8, ot), deflateInit2: Bt, deflateReset: Ct, deflateResetKeep: It, deflateSetHeader: (t2, e2) => Nt(t2) || 2 !== t2.state.wrap ? at : (t2.state.gzhead = e2, tt), deflate: (t2, e2) => {
    if (Nt(t2) || e2 > $ || e2 < 0) return t2 ? mt(t2, at) : at;
    const a2 = t2.state;
    if (!t2.output || 0 !== t2.avail_in && !t2.input || a2.status === wt && e2 !== V) return mt(t2, 0 === t2.avail_out ? nt : at);
    const s2 = a2.last_flush;
    if (a2.last_flush = e2, 0 !== a2.pending) {
      if (kt(t2), 0 === t2.avail_out) return a2.last_flush = -1, tt;
    } else if (0 === t2.avail_in && bt(e2) <= bt(s2) && e2 !== V) return mt(t2, nt);
    if (a2.status === wt && 0 !== t2.avail_in) return mt(t2, nt);
    if (a2.status === pt && 0 === a2.wrap && (a2.status = gt), a2.status === pt) {
      let e3 = ut + (a2.w_bits - 8 << 4) << 8, s3 = -1;
      if (s3 = a2.strategy >= _t || a2.level < 2 ? 0 : a2.level < 6 ? 1 : 6 === a2.level ? 2 : 3, e3 |= s3 << 6, 0 !== a2.strstart && (e3 |= 32), e3 += 31 - e3 % 31, Et(a2, e3), 0 !== a2.strstart && (Et(a2, t2.adler >>> 16), Et(a2, 65535 & t2.adler)), t2.adler = 1, a2.status = gt, kt(t2), 0 !== a2.pending) return a2.last_flush = -1, tt;
    }
    if (57 === a2.status) {
      if (t2.adler = 0, At(a2, 31), At(a2, 139), At(a2, 8), a2.gzhead) At(a2, (a2.gzhead.text ? 1 : 0) + (a2.gzhead.hcrc ? 2 : 0) + (a2.gzhead.extra ? 4 : 0) + (a2.gzhead.name ? 8 : 0) + (a2.gzhead.comment ? 16 : 0)), At(a2, 255 & a2.gzhead.time), At(a2, a2.gzhead.time >> 8 & 255), At(a2, a2.gzhead.time >> 16 & 255), At(a2, a2.gzhead.time >> 24 & 255), At(a2, 9 === a2.level ? 2 : a2.strategy >= _t || a2.level < 2 ? 4 : 0), At(a2, 255 & a2.gzhead.os), a2.gzhead.extra && a2.gzhead.extra.length && (At(a2, 255 & a2.gzhead.extra.length), At(a2, a2.gzhead.extra.length >> 8 & 255)), a2.gzhead.hcrc && (t2.adler = M(t2.adler, a2.pending_buf, a2.pending, 0)), a2.gzindex = 0, a2.status = 69;
      else if (At(a2, 0), At(a2, 0), At(a2, 0), At(a2, 0), At(a2, 0), At(a2, 9 === a2.level ? 2 : a2.strategy >= _t || a2.level < 2 ? 4 : 0), At(a2, 3), a2.status = gt, kt(t2), 0 !== a2.pending) return a2.last_flush = -1, tt;
    }
    if (69 === a2.status) {
      if (a2.gzhead.extra) {
        let e3 = a2.pending, s3 = (65535 & a2.gzhead.extra.length) - a2.gzindex;
        for (; a2.pending + s3 > a2.pending_buf_size; ) {
          let n3 = a2.pending_buf_size - a2.pending;
          if (a2.pending_buf.set(a2.gzhead.extra.subarray(a2.gzindex, a2.gzindex + n3), a2.pending), a2.pending = a2.pending_buf_size, a2.gzhead.hcrc && a2.pending > e3 && (t2.adler = M(t2.adler, a2.pending_buf, a2.pending - e3, e3)), a2.gzindex += n3, kt(t2), 0 !== a2.pending) return a2.last_flush = -1, tt;
          e3 = 0, s3 -= n3;
        }
        let n2 = new Uint8Array(a2.gzhead.extra);
        a2.pending_buf.set(n2.subarray(a2.gzindex, a2.gzindex + s3), a2.pending), a2.pending += s3, a2.gzhead.hcrc && a2.pending > e3 && (t2.adler = M(t2.adler, a2.pending_buf, a2.pending - e3, e3)), a2.gzindex = 0;
      }
      a2.status = 73;
    }
    if (73 === a2.status) {
      if (a2.gzhead.name) {
        let e3, s3 = a2.pending;
        do {
          if (a2.pending === a2.pending_buf_size) {
            if (a2.gzhead.hcrc && a2.pending > s3 && (t2.adler = M(t2.adler, a2.pending_buf, a2.pending - s3, s3)), kt(t2), 0 !== a2.pending) return a2.last_flush = -1, tt;
            s3 = 0;
          }
          e3 = a2.gzindex < a2.gzhead.name.length ? 255 & a2.gzhead.name.charCodeAt(a2.gzindex++) : 0, At(a2, e3);
        } while (0 !== e3);
        a2.gzhead.hcrc && a2.pending > s3 && (t2.adler = M(t2.adler, a2.pending_buf, a2.pending - s3, s3)), a2.gzindex = 0;
      }
      a2.status = 91;
    }
    if (91 === a2.status) {
      if (a2.gzhead.comment) {
        let e3, s3 = a2.pending;
        do {
          if (a2.pending === a2.pending_buf_size) {
            if (a2.gzhead.hcrc && a2.pending > s3 && (t2.adler = M(t2.adler, a2.pending_buf, a2.pending - s3, s3)), kt(t2), 0 !== a2.pending) return a2.last_flush = -1, tt;
            s3 = 0;
          }
          e3 = a2.gzindex < a2.gzhead.comment.length ? 255 & a2.gzhead.comment.charCodeAt(a2.gzindex++) : 0, At(a2, e3);
        } while (0 !== e3);
        a2.gzhead.hcrc && a2.pending > s3 && (t2.adler = M(t2.adler, a2.pending_buf, a2.pending - s3, s3));
      }
      a2.status = 103;
    }
    if (103 === a2.status) {
      if (a2.gzhead.hcrc) {
        if (a2.pending + 2 > a2.pending_buf_size && (kt(t2), 0 !== a2.pending)) return a2.last_flush = -1, tt;
        At(a2, 255 & t2.adler), At(a2, t2.adler >> 8 & 255), t2.adler = 0;
      }
      if (a2.status = gt, kt(t2), 0 !== a2.pending) return a2.last_flush = -1, tt;
    }
    if (0 !== t2.avail_in || 0 !== a2.lookahead || e2 !== q && a2.status !== wt) {
      let s3 = 0 === a2.level ? St(a2, e2) : a2.strategy === _t ? ((t3, e3) => {
        let a3;
        for (; ; ) {
          if (0 === t3.lookahead && (Rt(t3), 0 === t3.lookahead)) {
            if (e3 === q) return 1;
            break;
          }
          if (t3.match_length = 0, a3 = X(t3, 0, t3.window[t3.strstart]), t3.lookahead--, t3.strstart++, a3 && (xt(t3, false), 0 === t3.strm.avail_out)) return 1;
        }
        return t3.insert = 0, e3 === V ? (xt(t3, true), 0 === t3.strm.avail_out ? 3 : 4) : t3.sym_next && (xt(t3, false), 0 === t3.strm.avail_out) ? 1 : 2;
      })(a2, e2) : a2.strategy === lt ? ((t3, e3) => {
        let a3, s4, n2, r2;
        const i2 = t3.window;
        for (; ; ) {
          if (t3.lookahead <= ft) {
            if (Rt(t3), t3.lookahead <= ft && e3 === q) return 1;
            if (0 === t3.lookahead) break;
          }
          if (t3.match_length = 0, t3.lookahead >= 3 && t3.strstart > 0 && (n2 = t3.strstart - 1, s4 = i2[n2], s4 === i2[++n2] && s4 === i2[++n2] && s4 === i2[++n2])) {
            r2 = t3.strstart + ft;
            do {
            } while (s4 === i2[++n2] && s4 === i2[++n2] && s4 === i2[++n2] && s4 === i2[++n2] && s4 === i2[++n2] && s4 === i2[++n2] && s4 === i2[++n2] && s4 === i2[++n2] && n2 < r2);
            t3.match_length = ft - (r2 - n2), t3.match_length > t3.lookahead && (t3.match_length = t3.lookahead);
          }
          if (t3.match_length >= 3 ? (a3 = X(t3, 1, t3.match_length - 3), t3.lookahead -= t3.match_length, t3.strstart += t3.match_length, t3.match_length = 0) : (a3 = X(t3, 0, t3.window[t3.strstart]), t3.lookahead--, t3.strstart++), a3 && (xt(t3, false), 0 === t3.strm.avail_out)) return 1;
        }
        return t3.insert = 0, e3 === V ? (xt(t3, true), 0 === t3.strm.avail_out ? 3 : 4) : t3.sym_next && (xt(t3, false), 0 === t3.strm.avail_out) ? 1 : 2;
      })(a2, e2) : Ot[a2.level].func(a2, e2);
      if (3 !== s3 && 4 !== s3 || (a2.status = wt), 1 === s3 || 3 === s3) return 0 === t2.avail_out && (a2.last_flush = -1), tt;
      if (2 === s3 && (e2 === J ? W(a2) : e2 !== $ && (Y(a2, 0, 0, false), e2 === Q && (yt(a2.head), 0 === a2.lookahead && (a2.strstart = 0, a2.block_start = 0, a2.insert = 0))), kt(t2), 0 === t2.avail_out)) return a2.last_flush = -1, tt;
    }
    return e2 !== V ? tt : a2.wrap <= 0 ? et : (2 === a2.wrap ? (At(a2, 255 & t2.adler), At(a2, t2.adler >> 8 & 255), At(a2, t2.adler >> 16 & 255), At(a2, t2.adler >> 24 & 255), At(a2, 255 & t2.total_in), At(a2, t2.total_in >> 8 & 255), At(a2, t2.total_in >> 16 & 255), At(a2, t2.total_in >> 24 & 255)) : (Et(a2, t2.adler >>> 16), Et(a2, 65535 & t2.adler)), kt(t2), a2.wrap > 0 && (a2.wrap = -a2.wrap), 0 !== a2.pending ? tt : et);
  }, deflateEnd: (t2) => {
    if (Nt(t2)) return at;
    const e2 = t2.state.status;
    return t2.state = null, e2 === gt ? mt(t2, st) : tt;
  }, deflateSetDictionary: (t2, e2) => {
    let a2 = e2.length;
    if (Nt(t2)) return at;
    const s2 = t2.state, n2 = s2.wrap;
    if (2 === n2 || 1 === n2 && s2.status !== pt || s2.lookahead) return at;
    if (1 === n2 && (t2.adler = B(t2.adler, e2, a2, 0)), s2.wrap = 0, a2 >= s2.w_size) {
      0 === n2 && (yt(s2.head), s2.strstart = 0, s2.block_start = 0, s2.insert = 0);
      let t3 = new Uint8Array(s2.w_size);
      t3.set(e2.subarray(a2 - s2.w_size, a2), 0), e2 = t3, a2 = s2.w_size;
    }
    const r2 = t2.avail_in, i2 = t2.next_in, _2 = t2.input;
    for (t2.avail_in = a2, t2.next_in = 0, t2.input = e2, Rt(s2); s2.lookahead >= 3; ) {
      let t3 = s2.strstart, e3 = s2.lookahead - 2;
      do {
        s2.ins_h = zt(s2, s2.ins_h, s2.window[t3 + 3 - 1]), s2.prev[t3 & s2.w_mask] = s2.head[s2.ins_h], s2.head[s2.ins_h] = t3, t3++;
      } while (--e3);
      s2.strstart = t3, s2.lookahead = 2, Rt(s2);
    }
    return s2.strstart += s2.lookahead, s2.block_start = s2.strstart, s2.insert = s2.lookahead, s2.lookahead = 0, s2.match_length = s2.prev_length = 2, s2.match_available = 0, t2.next_in = i2, t2.input = _2, t2.avail_in = r2, s2.wrap = n2, tt;
  }, deflateInfo: "pako deflate (from Nodeca project)" };
  const Mt = (t2, e2) => Object.prototype.hasOwnProperty.call(t2, e2);
  var Pt = function(t2) {
    const e2 = Array.prototype.slice.call(arguments, 1);
    for (; e2.length; ) {
      const a2 = e2.shift();
      if (a2) {
        if ("object" != typeof a2) throw new TypeError(a2 + "must be non-object");
        for (const e3 in a2) Mt(a2, e3) && (t2[e3] = a2[e3]);
      }
    }
    return t2;
  }, jt = (t2) => {
    let e2 = 0;
    for (let a3 = 0, s2 = t2.length; a3 < s2; a3++) e2 += t2[a3].length;
    const a2 = new Uint8Array(e2);
    for (let e3 = 0, s2 = 0, n2 = t2.length; e3 < n2; e3++) {
      let n3 = t2[e3];
      a2.set(n3, s2), s2 += n3.length;
    }
    return a2;
  };
  let Kt = true;
  try {
    String.fromCharCode.apply(null, new Uint8Array(1));
  } catch (t2) {
    Kt = false;
  }
  const Yt = new Uint8Array(256);
  for (let t2 = 0; t2 < 256; t2++) Yt[t2] = t2 >= 252 ? 6 : t2 >= 248 ? 5 : t2 >= 240 ? 4 : t2 >= 224 ? 3 : t2 >= 192 ? 2 : 1;
  Yt[254] = Yt[254] = 1;
  var Gt = (t2) => {
    if ("function" == typeof TextEncoder && TextEncoder.prototype.encode) return new TextEncoder().encode(t2);
    let e2, a2, s2, n2, r2, i2 = t2.length, _2 = 0;
    for (n2 = 0; n2 < i2; n2++) a2 = t2.charCodeAt(n2), 55296 == (64512 & a2) && n2 + 1 < i2 && (s2 = t2.charCodeAt(n2 + 1), 56320 == (64512 & s2) && (a2 = 65536 + (a2 - 55296 << 10) + (s2 - 56320), n2++)), _2 += a2 < 128 ? 1 : a2 < 2048 ? 2 : a2 < 65536 ? 3 : 4;
    for (e2 = new Uint8Array(_2), r2 = 0, n2 = 0; r2 < _2; n2++) a2 = t2.charCodeAt(n2), 55296 == (64512 & a2) && n2 + 1 < i2 && (s2 = t2.charCodeAt(n2 + 1), 56320 == (64512 & s2) && (a2 = 65536 + (a2 - 55296 << 10) + (s2 - 56320), n2++)), a2 < 128 ? e2[r2++] = a2 : a2 < 2048 ? (e2[r2++] = 192 | a2 >>> 6, e2[r2++] = 128 | 63 & a2) : a2 < 65536 ? (e2[r2++] = 224 | a2 >>> 12, e2[r2++] = 128 | a2 >>> 6 & 63, e2[r2++] = 128 | 63 & a2) : (e2[r2++] = 240 | a2 >>> 18, e2[r2++] = 128 | a2 >>> 12 & 63, e2[r2++] = 128 | a2 >>> 6 & 63, e2[r2++] = 128 | 63 & a2);
    return e2;
  };
  var Xt = function() {
    this.input = null, this.next_in = 0, this.avail_in = 0, this.total_in = 0, this.output = null, this.next_out = 0, this.avail_out = 0, this.total_out = 0, this.msg = "", this.state = null, this.data_type = 2, this.adler = 0;
  };
  const Wt = Object.prototype.toString, { Z_NO_FLUSH: qt, Z_SYNC_FLUSH: Jt, Z_FULL_FLUSH: Qt, Z_FINISH: Vt, Z_OK: $t, Z_STREAM_END: te, Z_DEFAULT_COMPRESSION: ee, Z_DEFAULT_STRATEGY: ae, Z_DEFLATED: se } = j;
  function ne(t2) {
    this.options = Pt({ level: ee, method: se, chunkSize: 16384, windowBits: 15, memLevel: 8, strategy: ae }, t2 || {});
    let e2 = this.options;
    e2.raw && e2.windowBits > 0 ? e2.windowBits = -e2.windowBits : e2.gzip && e2.windowBits > 0 && e2.windowBits < 16 && (e2.windowBits += 16), this.err = 0, this.msg = "", this.ended = false, this.chunks = [], this.strm = new Xt(), this.strm.avail_out = 0;
    let a2 = Ht.deflateInit2(this.strm, e2.level, e2.method, e2.windowBits, e2.memLevel, e2.strategy);
    if (a2 !== $t) throw new Error(P[a2]);
    if (e2.header && Ht.deflateSetHeader(this.strm, e2.header), e2.dictionary) {
      let t3;
      if (t3 = "string" == typeof e2.dictionary ? Gt(e2.dictionary) : "[object ArrayBuffer]" === Wt.call(e2.dictionary) ? new Uint8Array(e2.dictionary) : e2.dictionary, a2 = Ht.deflateSetDictionary(this.strm, t3), a2 !== $t) throw new Error(P[a2]);
      this._dict_set = true;
    }
  }
  function re(t2, e2) {
    const a2 = new ne(e2);
    if (a2.push(t2, true), a2.err) throw a2.msg || P[a2.err];
    return a2.result;
  }
  ne.prototype.push = function(t2, e2) {
    const a2 = this.strm, s2 = this.options.chunkSize;
    let n2, r2;
    if (this.ended) return false;
    for (r2 = e2 === ~~e2 ? e2 : true === e2 ? Vt : qt, "string" == typeof t2 ? a2.input = Gt(t2) : "[object ArrayBuffer]" === Wt.call(t2) ? a2.input = new Uint8Array(t2) : a2.input = t2, a2.next_in = 0, a2.avail_in = a2.input.length; ; ) if (0 === a2.avail_out && (a2.output = new Uint8Array(s2), a2.next_out = 0, a2.avail_out = s2), (r2 === Jt || r2 === Qt) && a2.avail_out <= 6) this.onData(a2.output.subarray(0, a2.next_out)), a2.avail_out = 0;
    else {
      if (n2 = Ht.deflate(a2, r2), n2 === te) return a2.next_out > 0 && this.onData(a2.output.subarray(0, a2.next_out)), n2 = Ht.deflateEnd(this.strm), this.onEnd(n2), this.ended = true, n2 === $t;
      if (0 !== a2.avail_out) {
        if (r2 > 0 && a2.next_out > 0) this.onData(a2.output.subarray(0, a2.next_out)), a2.avail_out = 0;
        else if (0 === a2.avail_in) break;
      } else this.onData(a2.output);
    }
    return true;
  }, ne.prototype.onData = function(t2) {
    this.chunks.push(t2);
  }, ne.prototype.onEnd = function(t2) {
    t2 === $t && (this.result = jt(this.chunks)), this.chunks = [], this.err = t2, this.msg = this.strm.msg;
  };
  var ie = ne, _e = re, le = function(t2, e2) {
    return (e2 = e2 || {}).raw = true, re(t2, e2);
  }, he = function(t2, e2) {
    return (e2 = e2 || {}).gzip = true, re(t2, e2);
  }, oe = j, de = { Deflate: ie, deflate: _e, deflateRaw: le, gzip: he, constants: oe };
  t.Deflate = ie, t.constants = oe, t.default = de, t.deflate = _e, t.deflateRaw = le, t.gzip = he, Object.defineProperty(t, "__esModule", { value: true });
}));
